import { 
  collection, 
  doc, 
  getDocs, 
  getDoc,
  setDoc, 
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  runTransaction,
  serverTimestamp,
  Timestamp
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscription,
  UserSubscriptionEntry,
  SubscriptionSource,
  SubscriptionEntryStatus,
  createStripeSubscriptionEntry,
  createPerkSubscriptionEntry,
  createGiveawaySubscriptionEntry,
  getActiveEntry,
  sortEntriesByPrecedence,
  SUBSCRIPTION_LIMITS
} from "./user-subscription.types"
import { UserSubscriptionService } from "./user-subscription.service"

/**
 * Enhanced subscription service that works with existing UserSubscription collection
 * but adds multi-entry support via subcollection
 */
export class EnhancedSubscriptionService {
  private static readonly COLLECTION = "userSubscriptions"
  private static readonly ENTRIES_SUBCOLLECTION = "entries"

  /**
   * Get user subscription with enhanced multi-entry data
   */
  static async getEnhancedUserSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      // Get main subscription document
      const subscription = await UserSubscriptionService.getUserSubscription(userId)
      if (!subscription) return null

      // Get entries from subcollection
      const entries = await this.getSubscriptionEntries(userId)
      
      // Calculate enhanced fields
      const activeEntry = getActiveEntry(entries)
      const pausedEntries = entries.filter(entry => entry.status === "paused")
      
      // Enhance subscription with multi-entry data
      const enhancedSubscription: UserSubscription = {
        ...subscription,
        activeSource: activeEntry?.source,
        activePrecedence: activeEntry?.precedence,
        hasMultipleEntries: entries.length > 1,
        totalEntries: entries.length,
        pausedSources: pausedEntries.map(entry => entry.source)
      }

      return enhancedSubscription
    } catch (error) {
      console.error("Error getting enhanced user subscription:", error)
      return null
    }
  }

  /**
   * Get subscription entries for a user
   */
  static async getSubscriptionEntries(userId: string): Promise<UserSubscriptionEntry[]> {
    try {
      const entriesRef = collection(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION)
      const q = query(entriesRef, orderBy("precedence", "asc"))
      const snapshot = await getDocs(q)
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserSubscriptionEntry[]
    } catch (error) {
      console.error("Error getting subscription entries:", error)
      return []
    }
  }

  /**
   * Add a subscription entry and update main subscription document
   */
  static async addSubscriptionEntry(
    userId: string,
    entryData: Omit<UserSubscriptionEntry, "id" | "createdAt" | "updatedAt">
  ): Promise<ServiceResponse<{ entryId: string; updatedSubscription: UserSubscription }>> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Create entry in subcollection
        const entryRef = doc(collection(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION))
        
        transaction.set(entryRef, {
          ...entryData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        })

        // Get all entries (including the new one we're adding)
        const existingEntries = await this.getSubscriptionEntries(userId)
        const allEntries = [...existingEntries, { id: entryRef.id, ...entryData } as UserSubscriptionEntry]
        
        // Recalculate precedence and update main subscription
        const updatedSubscription = await this.updateMainSubscriptionFromEntries(userId, allEntries, transaction)
        
        return {
          success: true,
          data: {
            entryId: entryRef.id,
            updatedSubscription
          }
        }
      })
    } catch (error) {
      console.error("Error adding subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Remove a subscription entry and update main subscription document
   */
  static async removeSubscriptionEntry(
    userId: string,
    entryId: string
  ): Promise<ServiceResponse<{ updatedSubscription: UserSubscription }>> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Delete entry from subcollection
        const entryRef = doc(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION, entryId)
        transaction.delete(entryRef)

        // Get remaining entries
        const remainingEntries = (await this.getSubscriptionEntries(userId))
          .filter(entry => entry.id !== entryId)
        
        // Update main subscription
        const updatedSubscription = await this.updateMainSubscriptionFromEntries(userId, remainingEntries, transaction)
        
        return {
          success: true,
          data: { updatedSubscription }
        }
      })
    } catch (error) {
      console.error("Error removing subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Update main subscription document based on entries
   */
  private static async updateMainSubscriptionFromEntries(
    userId: string,
    entries: UserSubscriptionEntry[],
    transaction?: any
  ): Promise<UserSubscription> {
    const activeEntry = getActiveEntry(entries)
    const pausedEntries = entries.filter(entry => entry.status === "paused")
    
    // Determine subscription data from active entry
    let subscriptionData: Partial<UserSubscription> = {
      activeSource: activeEntry?.source,
      activePrecedence: activeEntry?.precedence,
      hasMultipleEntries: entries.length > 1,
      totalEntries: entries.length,
      pausedSources: pausedEntries.map(entry => entry.source)
    }

    if (activeEntry) {
      subscriptionData = {
        ...subscriptionData,
        subscriptionPlan: activeEntry.subscriptionPlan,
        subscriptionStatus: activeEntry.stripeData?.subscriptionStatus || "active"
      }

      // If active entry is Stripe, update Stripe-specific fields
      if (activeEntry.source === "stripe" && activeEntry.stripeData) {
        subscriptionData = {
          ...subscriptionData,
          stripeCustomerId: activeEntry.stripeData.customerId,
          subscriptionId: activeEntry.stripeData.subscriptionId,
          subscriptionCurrentPeriodEnd: activeEntry.stripeData.currentPeriodEnd
        }
      }
    } else {
      // No active entry - set to free
      subscriptionData = {
        ...subscriptionData,
        subscriptionPlan: "free",
        subscriptionStatus: null,
        stripeCustomerId: "",
        subscriptionId: "",
        subscriptionCurrentPeriodEnd: null
      }
    }

    // Update main subscription document
    const subscriptionRef = doc(db, this.COLLECTION, userId)
    const updateData = {
      ...subscriptionData,
      updatedAt: serverTimestamp()
    }

    if (transaction) {
      transaction.update(subscriptionRef, updateData)
    } else {
      await updateDoc(subscriptionRef, updateData)
    }

    // Return updated subscription
    const updatedDoc = await getDoc(subscriptionRef)
    return { id: updatedDoc.id, ...updatedDoc.data() } as UserSubscription
  }

  /**
   * Add Stripe subscription entry
   */
  static async addStripeSubscription(
    userId: string,
    stripeData: {
      customerId: string
      subscriptionId: string
      subscriptionStatus: any
      currentPeriodEnd: number | Timestamp
    },
    plan: "monthly" | "yearly"
  ): Promise<ServiceResponse> {
    const entryData = createStripeSubscriptionEntry(userId, stripeData, plan)
    const result = await this.addSubscriptionEntry(userId, entryData)
    return { success: result.success, error: result.error }
  }

  /**
   * Add perk subscription entry
   */
  static async addPerkSubscription(
    userId: string,
    perkData: {
      perkId: string
      appliedAt: Timestamp
      duration: number
    },
    plan: "monthly" | "yearly" = "monthly"
  ): Promise<ServiceResponse> {
    const entryData = createPerkSubscriptionEntry(userId, perkData, plan)
    const result = await this.addSubscriptionEntry(userId, entryData)
    return { success: result.success, error: result.error }
  }

  /**
   * Add giveaway subscription entry
   */
  static async addGiveawaySubscription(
    userId: string,
    giveawayData: {
      giveawayId: string
      duration: number
    },
    plan: "monthly" | "yearly" = "monthly"
  ): Promise<ServiceResponse> {
    const entryData = createGiveawaySubscriptionEntry(userId, giveawayData, plan)
    const result = await this.addSubscriptionEntry(userId, entryData)
    return { success: result.success, error: result.error }
  }

  /**
   * Process expired entries and update main subscription
   */
  static async processExpiredEntries(userId: string): Promise<ServiceResponse<{
    expiredCount: number
    updatedSubscription: UserSubscription
  }>> {
    try {
      const entries = await this.getSubscriptionEntries(userId)
      const now = new Date()
      let expiredCount = 0

      // Mark expired entries
      for (const entry of entries) {
        if (entry.endDate && entry.status !== "expired") {
          const endDate = entry.endDate instanceof Date ? entry.endDate : entry.endDate.toDate()
          if (endDate <= now) {
            await updateDoc(
              doc(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION, entry.id),
              { status: "expired", updatedAt: serverTimestamp() }
            )
            expiredCount++
          }
        }
      }

      // Get updated entries and refresh main subscription
      const updatedEntries = await this.getSubscriptionEntries(userId)
      const updatedSubscription = await this.updateMainSubscriptionFromEntries(userId, updatedEntries)

      return {
        success: true,
        data: { expiredCount, updatedSubscription }
      }
    } catch (error) {
      console.error("Error processing expired entries:", error)
      return { success: false, error }
    }
  }

  /**
   * Get subscription summary for display
   */
  static async getSubscriptionSummary(userId: string): Promise<{
    isSubscribed: boolean
    currentPlan: string
    currentSource: string | null
    totalEntries: number
    activeEntries: number
    pausedEntries: number
    expiredEntries: number
  }> {
    try {
      const subscription = await this.getEnhancedUserSubscription(userId)
      const entries = await this.getSubscriptionEntries(userId)

      return {
        isSubscribed: subscription?.subscriptionPlan !== "free",
        currentPlan: subscription?.subscriptionPlan || "free",
        currentSource: subscription?.activeSource || null,
        totalEntries: entries.length,
        activeEntries: entries.filter(e => e.status === "applied").length,
        pausedEntries: entries.filter(e => e.status === "paused").length,
        expiredEntries: entries.filter(e => e.status === "expired").length
      }
    } catch (error) {
      console.error("Error getting subscription summary:", error)
      return {
        isSubscribed: false,
        currentPlan: "free",
        currentSource: null,
        totalEntries: 0,
        activeEntries: 0,
        pausedEntries: 0,
        expiredEntries: 0
      }
    }
  }
}
