import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  type Timestamp,
  setDoc,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscription,
  UserSubscriptionCreateData,
  UserSubscriptionUpdateData,
  AggregatedSubscriptionState,
  createStripeSubscriptionEntry,
  StripeSubscriptionData,
} from "./user-subscription.types"
import { SubscriptionAggregationService } from "./subscription-aggregation.service"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"
import { SubscriptionPrecedenceService } from "./subscription-precedence.service"

/**
 * User subscription service for Firebase operations
 */
export class UserSubscriptionService {
  private static readonly COLLECTION = "userSubscriptions"

  /**
   * Get a user's subscription
   * @param userId User ID
   * @returns The user subscription or null if not found
   */
  static async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      const subscriptionDoc = await getDoc(doc(db, this.COLLECTION, userId))

      if (subscriptionDoc.exists()) {
        return { id: subscriptionDoc.id, ...subscriptionDoc.data() } as UserSubscription
      }

      // If no subscription document exists, create a default free subscription
      const defaultSubscription: UserSubscriptionCreateData = {
        userId,
        stripeCustomerId: "",
        subscriptionId: "",
        subscriptionStatus: null,
        subscriptionPlan: "free",
        subscriptionCurrentPeriodEnd: null,
      }

      // Try to create the subscription document
      try {
        const result = await this.createUserSubscription(userId, defaultSubscription)
        if (result.success) {
          return {
            id: userId,
            ...defaultSubscription,
            createdAt: serverTimestamp() as Timestamp,
            updatedAt: serverTimestamp() as Timestamp,
          } as UserSubscription
        }
      } catch (createError) {
        // If creation fails due to permissions or other issues, just return null
        console.warn("Could not create default subscription:", createError)
      }

      return null
    } catch (error) {
      console.error("Error getting user subscription:", error)
      return null
    }
  }

  /**
   * Create a user subscription
   * @param userId User ID
   * @param subscriptionData Subscription data
   * @returns Service response
   */
  static async createUserSubscription(
    userId: string,
    subscriptionData: UserSubscriptionCreateData
  ): Promise<ServiceResponse> {
    try {
      await setDoc(doc(db, this.COLLECTION, userId), {
        ...subscriptionData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error creating user subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Update a user's subscription
   * @param userId User ID
   * @param subscriptionData Subscription data to update
   * @returns Service response
   */
  static async updateUserSubscription(
    userId: string,
    subscriptionData: UserSubscriptionUpdateData
  ): Promise<ServiceResponse> {
    try {
      const subscriptionRef = doc(db, this.COLLECTION, userId)
      const subscriptionDoc = await getDoc(subscriptionRef)

      if (subscriptionDoc.exists()) {
        // Update existing subscription
        await updateDoc(subscriptionRef, {
          ...subscriptionData,
          updatedAt: serverTimestamp(),
        })
      } else {
        // Create new subscription if it doesn't exist
        await this.createUserSubscription(userId, {
          userId,
          stripeCustomerId: subscriptionData.stripeCustomerId || "",
          subscriptionId: subscriptionData.subscriptionId || "",
          subscriptionStatus: subscriptionData.subscriptionStatus || null,
          subscriptionPlan: subscriptionData.subscriptionPlan || "free",
          subscriptionCurrentPeriodEnd: subscriptionData.subscriptionCurrentPeriodEnd || null,
        })
      }

      return { success: true }
    } catch (error) {
      console.error("Error updating user subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user has an active subscription
   * @param userId User ID
   * @returns True if the user has an active subscription
   */
  static async hasActiveSubscription(userId: string): Promise<boolean> {
    try {
      const subscription = await this.getUserSubscription(userId)

      if (!subscription) return false

      return (
        subscription.subscriptionStatus === "active" &&
        (subscription.subscriptionPlan === "monthly" ||
          subscription.subscriptionPlan === "yearly") &&
        (subscription.subscriptionCurrentPeriodEnd
          ? (() => {
              try {
                // Handle Firestore Timestamp objects
                if (
                  subscription.subscriptionCurrentPeriodEnd &&
                  subscription.subscriptionCurrentPeriodEnd !== null &&
                  typeof subscription.subscriptionCurrentPeriodEnd === "object" &&
                  typeof (subscription.subscriptionCurrentPeriodEnd as any).toDate === "function"
                ) {
                  const endDate = (subscription.subscriptionCurrentPeriodEnd as any).toDate()
                  if (endDate && endDate instanceof Date) {
                    return endDate > new Date()
                  }
                  console.error("toDate() returned invalid date:", endDate)
                  return true // Default to true to avoid blocking users
                }
                // Handle numeric timestamps (seconds)
                else if (typeof subscription.subscriptionCurrentPeriodEnd === "number") {
                  const timestamp = subscription.subscriptionCurrentPeriodEnd
                  // If timestamp is in seconds (before year 2033), convert to milliseconds
                  const milliseconds = timestamp < 2000000000 ? timestamp * 1000 : timestamp
                  return new Date(milliseconds) > new Date()
                }
                // Handle any other unexpected format
                else {
                  console.error(
                    "Unexpected subscriptionCurrentPeriodEnd format:",
                    subscription.subscriptionCurrentPeriodEnd
                  )
                  return true // Default to true to avoid blocking users
                }
              } catch (error) {
                console.error("Error checking subscription expiration:", error)
                return true // Default to true to avoid blocking users
              }
            })()
          : true)
      )
    } catch (error) {
      console.error("Error checking if user has active subscription:", error)
      return false
    }
  }

  /**
   * Check if a user is subscribed (with error handling)
   * This is a safer version of hasActiveSubscription that handles errors gracefully
   * and is designed to be used in UI components
   * @param userId User ID
   * @returns True if the user is subscribed
   */
  static async isUserSubscribed(userId: string): Promise<boolean> {
    try {
      // If no userId is provided, return false
      if (!userId) return false

      const subscription = await this.getUserSubscription(userId)
      if (!subscription) return false

      // Check if subscription is active
      const isActive =
        subscription.subscriptionStatus === "active" &&
        (subscription.subscriptionPlan === "monthly" || subscription.subscriptionPlan === "yearly")

      if (!isActive) return false

      // Check if subscription is expired based on end date
      try {
        // Handle Firestore Timestamp objects
        if (
          subscription.subscriptionCurrentPeriodEnd &&
          subscription.subscriptionCurrentPeriodEnd !== null &&
          typeof subscription.subscriptionCurrentPeriodEnd === "object" &&
          typeof (subscription.subscriptionCurrentPeriodEnd as any).toDate === "function"
        ) {
          const endDate = (subscription.subscriptionCurrentPeriodEnd as any).toDate()
          if (endDate && endDate instanceof Date) {
            return endDate > new Date()
          }
          console.error("toDate() returned invalid date:", endDate)
          return true // Default to true to avoid blocking users
        }
        // Handle numeric timestamps (seconds)
        else if (typeof subscription.subscriptionCurrentPeriodEnd === "number") {
          const timestamp = subscription.subscriptionCurrentPeriodEnd
          // If timestamp is in seconds (before year 2033), convert to milliseconds
          const milliseconds = timestamp < 2000000000 ? timestamp * 1000 : timestamp
          return new Date(milliseconds) > new Date()
        }
        // Handle any other format (like string dates)
        else if (subscription.subscriptionCurrentPeriodEnd) {
          try {
            // Try to parse as a date string
            return new Date(subscription.subscriptionCurrentPeriodEnd as any) > new Date()
          } catch (e) {
            console.error("Failed to parse subscription end date:", e)
            return true // Default to true to avoid blocking users
          }
        }

        return true // Default to true if we can't determine
      } catch (error) {
        console.error("Error checking subscription expiration:", error)
        return true // Default to true to avoid blocking users
      }
    } catch (error) {
      console.error("Error checking if user is subscribed:", error)
      return false // Default to false on error
    }
  }

  /**
   * Get all user subscriptions
   * @returns Array of user subscriptions
   */
  static async getAllUserSubscriptions(): Promise<UserSubscription[]> {
    try {
      const querySnapshot = await getDocs(collection(db, this.COLLECTION))
      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserSubscription[]
    } catch (error) {
      console.error("Error getting all user subscriptions:", error)
      return []
    }
  }

  // ===== NEW MULTI-ENTRY SUBSCRIPTION METHODS =====

  /**
   * Get comprehensive subscription state using new multi-entry system
   * @param userId User ID
   * @returns Aggregated subscription state
   */
  static async getComprehensiveSubscriptionState(
    userId: string
  ): Promise<AggregatedSubscriptionState> {
    return await SubscriptionAggregationService.getComprehensiveSubscriptionState(userId)
  }

  /**
   * Create a Stripe subscription entry in the new system
   * @param userId User ID
   * @param stripeData Stripe subscription data
   * @param plan Subscription plan
   * @returns Service response
   */
  static async createStripeSubscriptionEntry(
    userId: string,
    stripeData: StripeSubscriptionData,
    plan: "monthly" | "yearly"
  ): Promise<ServiceResponse> {
    try {
      const entryData = createStripeSubscriptionEntry(userId, stripeData, plan)
      const result = await SubscriptionPrecedenceService.addSubscriptionWithPrecedence(entryData)

      return { success: result.success, error: result.error }
    } catch (error) {
      console.error("Error creating Stripe subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Update Stripe subscription entry
   * @param userId User ID
   * @param stripeData Updated Stripe data
   * @returns Service response
   */
  static async updateStripeSubscriptionEntry(
    userId: string,
    stripeData: Partial<StripeSubscriptionData>
  ): Promise<ServiceResponse> {
    try {
      // Get existing Stripe entry
      const stripeEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "stripe")

      if (stripeEntries.length === 0) {
        return { success: false, error: new Error("No Stripe subscription entry found") }
      }

      const stripeEntry = stripeEntries[0]
      const updatedStripeData = { ...stripeEntry.stripeData, ...stripeData }

      const result = await UserSubscriptionEntryService.updateSubscriptionEntry(
        userId,
        stripeEntry.id,
        { stripeData: updatedStripeData }
      )

      // Recalculate precedence after update
      if (result.success) {
        await SubscriptionPrecedenceService.recalculatePrecedence(userId)
      }

      return result
    } catch (error) {
      console.error("Error updating Stripe subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Cancel Stripe subscription (remove entry)
   * @param userId User ID
   * @returns Service response
   */
  static async cancelStripeSubscription(userId: string): Promise<ServiceResponse> {
    try {
      // Get existing Stripe entry
      const stripeEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "stripe")

      if (stripeEntries.length === 0) {
        return { success: true } // Already cancelled
      }

      const stripeEntry = stripeEntries[0]
      return await SubscriptionPrecedenceService.removeSubscriptionWithPrecedence(
        userId,
        stripeEntry.id
      )
    } catch (error) {
      console.error("Error cancelling Stripe subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Get enhanced subscription limits (perk-aware)
   * @param userId User ID
   * @returns Enhanced limits
   */
  static async getEnhancedSubscriptionLimits(userId: string) {
    return await SubscriptionAggregationService.getEnhancedSubscriptionLimits(userId)
  }

  /**
   * Check if user has access to a specific feature
   * @param userId User ID
   * @param feature Feature name
   * @returns Whether user has access
   */
  static async hasFeatureAccess(userId: string, feature: string): Promise<boolean> {
    return await SubscriptionAggregationService.hasFeatureAccess(userId, feature)
  }

  /**
   * Get subscription summary for display
   * @param userId User ID
   * @returns Subscription summary
   */
  static async getSubscriptionSummary(userId: string) {
    return await SubscriptionAggregationService.getSubscriptionSummary(userId)
  }

  /**
   * Force recalculate subscription precedence
   * @param userId User ID
   * @returns Service response
   */
  static async forceRecalculatePrecedence(userId: string): Promise<ServiceResponse> {
    return await SubscriptionPrecedenceService.forceRecalculatePrecedence(userId)
  }
}
