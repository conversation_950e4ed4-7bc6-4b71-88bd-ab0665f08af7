# Task: Fix Simplified Subscription Cleanup

## Overview

Complete the migration from the old user-subscription architecture to the new flat subscription service. This task addresses remaining dependencies and ensures all components use the new flat architecture.

## Context

- ✅ **Phase 1 Complete**: Fixed syntax errors in perk-aware-subscription.service.ts
- ✅ **Phase 2 Complete**: Fixed broken imports in user-subscription.service.ts and subscription-aggregation.service.ts
- ✅ **Phase 3 Complete**: Fixed component-side errors (SubscriptionInitializer, hooks, store selectors)
- 🔄 **Phase 4 In Progress**: Migrate remaining services and components to use FlatSubscriptionService

## Current Status: Phase 4 - Complete Migration

### Phase 4.1: API Routes Migration ✅

- [x] **Migrate API routes to use FlatSubscriptionService**
  - [x] `app/api/places/search/route.ts` - Replace `UserSubscriptionService.isUserSubscribed` with `FlatSubscriptionService.hasFeatureAccess`
  - [x] `app/api/places/activity-search/route.ts` - Replace `UserSubscriptionService.isUserSubscribed` with `FlatSubscriptionService.hasFeatureAccess`

### Phase 4.2: Component Migration ✅

- [x] **Migrate squad member components**
  - [x] `app/(authenticated)/squads/[id]/components/members/members-tab.tsx` - Replace subscription check logic with `FlatSubscriptionService.getCurrentSubscription`

### Phase 4.3: Service Layer Migration ✅

- [x] **Migrate activity preferences service**
  - [x] `lib/domains/activity-preferences/activity-preferences.service.ts` - Replace all `UserSubscriptionService` calls with `FlatSubscriptionService.hasFeatureAccess`
- [x] **Migrate user preferences service**
  - [x] `lib/domains/user-preferences/user-preferences.service.ts` - Replace subscription checks with `FlatSubscriptionService.hasFeatureAccess`
- [x] **Update user preferences types**
  - [x] `lib/domains/user-preferences/user-preferences.types.ts` - Replace subscription validation with `FlatSubscriptionService.hasFeatureAccess`

### Phase 4.4: Realtime Services Migration ✅

- [x] **Migrate realtime subscription service**
  - [x] `lib/domains/user-subscription/user-subscription.realtime.service.ts` - Update to use flat structure with query-based subscriptions
- [x] **Migrate realtime subscription hooks**
  - [x] `lib/domains/user-subscription/user-subscription.realtime.hooks.ts` - Update to use `UserSubscriptionEntry` type

### Phase 4.5: Store and State Management

- [ ] **Update subscription store**
  - [ ] `lib/domains/user-subscription/user-subscription.store.ts` - Remove remaining legacy service calls
- [ ] **Validate store integration**
  - [ ] Ensure all store methods use FlatSubscriptionService
  - [ ] Test store state management with flat architecture

### Phase 4.6: Index and Export Cleanup

- [ ] **Update domain exports**
  - [ ] `lib/domains/user-subscription/index.ts` - Mark legacy services as deprecated
  - [ ] `lib/domains/index.ts` - Update exports to prioritize flat services

### Phase 4.7: Enhanced Services Cleanup

- [ ] **Review enhanced subscription service**
  - [ ] `lib/domains/user-subscription/enhanced-subscription.service.ts` - Determine if still needed
- [ ] **Clean up migration services**
  - [ ] Review `lib/domains/user-subscription/clean-migration.service.ts` usage
  - [ ] Update migration scripts if needed

## Validation Phase

- [ ] **Test all subscription-related functionality**
  - [ ] User subscription status checks
  - [ ] Squad member pro badges
  - [ ] Activity preferences access
  - [ ] API route subscription validation
  - [ ] Realtime subscription updates

## Success Criteria

- [ ] All components use FlatSubscriptionService or SubscriptionAggregationService
- [ ] No direct imports of UserSubscriptionService in active code
- [ ] All subscription checks work correctly
- [ ] Realtime updates work with flat structure
- [ ] Squad member pro badges display correctly
- [ ] API routes properly validate subscriptions

## Notes

- Legacy UserSubscriptionService is kept for backward compatibility but marked as deprecated
- All new functionality should use FlatSubscriptionService
- Realtime hooks need to be updated to work with the flat collection structure
- Multi-user subscription queries (for squad badges) should use the new flat structure

## Dependencies

- FlatSubscriptionService must be fully functional
- SubscriptionAggregationService must handle all complex queries
- Database migration to flat structure should be complete

---

**Last Updated**: 2025-01-06
**Status**: Phase 4 In Progress
**Next Action**: Migrate API routes to use FlatSubscriptionService
